"""
/support-summary command implementation.

Handles support summary generation requests.
"""

import logging
from typing import List, <PERSON>ple

from ..support_summary_command import handle_support_summary_command as handle_support_summary_command_logic

logger = logging.getLogger(__name__)


def handle_support_summary_command(command_text: str = "") -> Tuple[bool, List[str]]:
    """Handle /support-summary command. Returns success status and response messages."""
    try:
        return handle_support_summary_command_logic(command_text)
    except Exception as e:
        logger.error(f"Error handling /support-summary command: {e}")
        return False, ["❌ Error: Failed to generate support summary"]
